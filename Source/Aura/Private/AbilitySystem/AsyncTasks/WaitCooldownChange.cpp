// Copyright iYuuki Studio


#include "AbilitySystem/AsyncTasks/WaitCooldownChange.h"

#include "AbilitySystemComponent.h"
#include "Aura/AuraLogChannel.h"

UWaitCooldownChange* UWaitCooldownChange::WaitCooldownChange(FGameplayTag InCooldownTag,
                                                             UAbilitySystemComponent* ASC)
{
	UWaitCooldownChange* WaitCooldownChange = NewObject<UWaitCooldownChange>();
	WaitCooldownChange->AbilitySystemComponent = ASC;
	WaitCooldownChange->CooldownTag = InCooldownTag;
	

	if (!IsValid(ASC))
	{
		WaitCooldownChange->EndWaitCooldownChange();
		return WaitCooldownChange;
	}

	ASC->OnActiveGameplayEffectAddedDelegateToSelf.AddUObject(WaitCooldownChange,
	                                                          &UWaitCooldownChange::OnActiveGameplayEffectAdded);

	ASC->RegisterGameplayTagEvent(InCooldownTag, EGameplayTagEventType::NewOrRemoved).AddUObject(
		WaitCooldownChange, &ThisClass::CooldownTagChanged);

	return WaitCooldownChange;
}

void UWaitCooldownChange::EndWaitCooldownChange()
{
	if (IsValid(AbilitySystemComponent))
	{
		AbilitySystemComponent->OnActiveGameplayEffectAddedDelegateToSelf.RemoveAll(this);
		AbilitySystemComponent->RegisterGameplayTagEvent(CooldownTag, EGameplayTagEventType::NewOrRemoved).RemoveAll(
			this);
	}

	SetReadyToDestroy();
	MarkAsGarbage();
}

void UWaitCooldownChange::OnActiveGameplayEffectAdded(UAbilitySystemComponent* ASC,
                                                      const FGameplayEffectSpec& EffectSpec,
                                                      FActiveGameplayEffectHandle ActiveHandle)
{
	FGameplayTagContainer AssetTags;
	EffectSpec.GetAllAssetTags(AssetTags);
	FGameplayTagContainer GrantedTags;
	EffectSpec.GetAllGrantedTags(GrantedTags);

	if (AssetTags.HasTagExact(CooldownTag) || GrantedTags.HasTagExact(CooldownTag))
	{
		FGameplayEffectQuery Query = FGameplayEffectQuery::MakeQuery_MatchAnyOwningTags(GrantedTags);
		TArray<float> Durations = ASC->GetActiveEffectsTimeRemaining(Query);
		if (Durations.Num() > 0)
		{
			float RemainingTime = Durations[0];

			for (int i = 1; i < Durations.Num(); ++i)
			{
				RemainingTime = FMath::Max(RemainingTime, Durations[i]);
			}
			OnCooldownStart.Broadcast(RemainingTime);
		}
	}
}

void UWaitCooldownChange::CooldownTagChanged(const FGameplayTag Tag, int32 NewCount)
{
	if (!Tag.IsValid()) return;
	if (NewCount <= 0)
	{
		OnCooldownEnd.Broadcast(0.f);
	}
}
