// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "Kismet/BlueprintAsyncActionBase.h"
#include "WaitCooldownChange.generated.h"

struct FActiveGameplayEffectHandle;
struct FGameplayEffectSpec;
class UAbilitySystemComponent;
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FCooldownChangeSignature, float, Cooldown);

/**
 * 
 */
UCLASS(BlueprintType, meta = (ExposedAsyncProxy = "AsyncTask"))
class AURA_API UWaitCooldownChange : public UBlueprintAsyncActionBase
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, meta = (BlueprintInternalUseOnly = "true"), Category = "Cooldown")
	static UWaitCooldownChange* WaitCooldownChange(const FGameplayTag& InCooldownTag,
	                                               UAbilitySystemComponent* ASC);

protected:
	UPROPERTY(BlueprintAssignable, Category = "Cooldown")
	FCooldownChangeSignature OnCooldownStart;

	UPROPERTY(BlueprintAssignable, Category = "Cooldown")
	FCooldownChangeSignature OnCooldownEnd;

	UFUNCTION(BlueprintCallable)
	void EndWaitCooldownChange();

	void OnActiveGameplayEffectAdded(UAbilitySystemComponent* ASC, const FGameplayEffectSpec& EffectSpec,
	                                 FActiveGameplayEffectHandle ActiveHandle);

private:
	void CooldownTagChanged(const FGameplayTag Tag, int32 NewCount);

	UPROPERTY()
	FGameplayTag CooldownTag;

	UPROPERTY()
	UAbilitySystemComponent* AbilitySystemComponent;
};
